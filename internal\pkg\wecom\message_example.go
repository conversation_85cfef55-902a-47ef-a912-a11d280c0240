package wecom

import (
	"fmt"
	"log"
)

// 使用示例
func ExampleSendTextMessage() {
	// 创建企微客户端
	client := NewWeComClient("your_corp_id", "your_corp_secret")
	if client == nil {
		log.Fatal("创建企微客户端失败")
	}

	// 发送简单文本消息
	resp, err := client.SendTextMessage(
		"UserID1|UserID2", // toUser
		"",                // toParty
		"",                // toTag
		1000001,           // agentID
		"你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"https://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。",
	)
	if err != nil {
		log.Printf("发送文本消息失败: %v", err)
		return
	}
	fmt.Printf("发送文本消息成功，消息ID: %s\n", resp.MsgID)

	// 发送带选项的文本消息
	resp2, err := client.SendTextMessage(
		"@all",    // 发送给所有人
		"",        // toParty
		"",        // toTag
		1000001,   // agentID
		"重要通知：请所有员工注意查收。",
		WithSafe(1),                    // 保密消息
		WithIDTrans(1),                 // 开启ID转译
		WithDuplicateCheck(1, 1800),    // 开启重复消息检查，间隔1800秒
	)
	if err != nil {
		log.Printf("发送保密文本消息失败: %v", err)
		return
	}
	fmt.Printf("发送保密文本消息成功，消息ID: %s\n", resp2.MsgID)
}

func ExampleSendNewsMessage() {
	// 创建企微客户端
	client := NewWeComClient("your_corp_id", "your_corp_secret")
	if client == nil {
		log.Fatal("创建企微客户端失败")
	}

	// 准备图文消息文章
	articles := []Article{
		{
			Title:       "中秋节礼品领取",
			Description: "今年中秋节公司有豪礼相送",
			URL:         "https://example.com/gift",
			PicURL:      "https://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png",
		},
		{
			Title:       "国庆节放假通知",
			Description: "国庆节放假安排及注意事项",
			URL:         "https://example.com/holiday",
			PicURL:      "https://example.com/holiday.jpg",
		},
	}

	// 发送图文消息
	resp, err := client.SendNewsMessage(
		"UserID1|UserID2", // toUser
		"",                // toParty
		"",                // toTag
		1000001,           // agentID
		articles,
	)
	if err != nil {
		log.Printf("发送图文消息失败: %v", err)
		return
	}
	fmt.Printf("发送图文消息成功，消息ID: %s\n", resp.MsgID)

	// 发送带小程序的图文消息
	articlesWithMiniProgram := []Article{
		{
			Title:       "查看工资条",
			Description: "点击查看本月工资详情",
			AppID:       "wx123123123123123",
			PagePath:    "pages/salary?userid=zhangsan",
			PicURL:      "https://example.com/salary.jpg",
		},
	}

	resp2, err := client.SendNewsMessage(
		"zhangsan",  // 发送给特定用户
		"",          // toParty
		"",          // toTag
		1000001,     // agentID
		articlesWithMiniProgram,
		WithIDTrans(1),                 // 开启ID转译
		WithDuplicateCheck(1, 3600),    // 开启重复消息检查，间隔3600秒
	)
	if err != nil {
		log.Printf("发送小程序图文消息失败: %v", err)
		return
	}
	fmt.Printf("发送小程序图文消息成功，消息ID: %s\n", resp2.MsgID)
}

func ExampleSendToTag() {
	// 创建企微客户端
	client := NewWeComClient("your_corp_id", "your_corp_secret")
	if client == nil {
		log.Fatal("创建企微客户端失败")
	}

	// 发送消息给特定标签的用户
	resp, err := client.SendTextMessage(
		"",        // toUser
		"",        // toParty
		"TagID1|TagID2", // toTag - 发送给标签用户
		1000001,   // agentID
		"标签用户专属消息：请查看最新的部门通知。",
	)
	if err != nil {
		log.Printf("发送标签消息失败: %v", err)
		return
	}
	fmt.Printf("发送标签消息成功，消息ID: %s\n", resp.MsgID)
}

func ExampleSendToParty() {
	// 创建企微客户端
	client := NewWeComClient("your_corp_id", "your_corp_secret")
	if client == nil {
		log.Fatal("创建企微客户端失败")
	}

	// 发送消息给特定部门
	resp, err := client.SendTextMessage(
		"",              // toUser
		"PartyID1|PartyID2", // toParty - 发送给部门
		"",              // toTag
		1000001,         // agentID
		"部门通知：明天上午10点开部门例会。",
	)
	if err != nil {
		log.Printf("发送部门消息失败: %v", err)
		return
	}
	fmt.Printf("发送部门消息成功，消息ID: %s\n", resp.MsgID)
}
