# 企业微信消息发送API文档

## 文本消息发送

### 简要描述
发送文本消息到企业微信，支持发送给指定用户、部门或标签。

### 请求方法
```go
func (client *Client) SendTextMessage(toUser, toParty, toTag string, agentID int, content string, options ...MessageOption) (*MessageResp, error)
```

### 参数说明

| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| toUser | 否 | string | 指定接收消息的成员，成员ID列表（多个接收者用'\|'分隔，最多支持1000个）。特殊情况：指定为"@all"，则向该企业应用的全部成员发送 |
| toParty | 否 | string | 指定接收消息的部门，部门ID列表，多个接收者用'\|'分隔，最多支持100个。当toUser为"@all"时忽略本参数 |
| toTag | 否 | string | 指定接收消息的标签，标签ID列表，多个接收者用'\|'分隔，最多支持100个。当toUser为"@all"时忽略本参数 |
| agentID | 是 | int | 企业应用的id，整型。企业内部开发，可在应用的设置页面查看 |
| content | 是 | string | 消息内容，最长不超过2048个字节，超过将截断（支持id转译） |
| options | 否 | ...MessageOption | 可选参数，支持WithSafe、WithIDTrans、WithDuplicateCheck |

### 返回示例
```json
{
  "errcode": 0,
  "errmsg": "ok",
  "invaliduser": "userid1|userid2",
  "invalidparty": "partyid1|partyid2",
  "invalidtag": "tagid1|tagid2",
  "unlicenseduser": "userid3|userid4",
  "msgid": "xxxx",
  "response_code": "xyzxyz"
}
```

### 返回参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| errcode | int | 返回码，0表示成功 |
| errmsg | string | 对返回码的文本描述内容 |
| invaliduser | string | 不合法的userid，不区分大小写，统一转为小写 |
| invalidparty | string | 不合法的partyid |
| invalidtag | string | 不合法的标签id |
| unlicenseduser | string | 没有基础接口许可(包含已过期)的userid |
| msgid | string | 消息id，用于撤回应用消息 |
| response_code | string | 仅消息类型为"按钮交互型"，"投票选择型"和"多项选择型"的模板卡片消息返回 |

### 使用示例
```go
// 基本用法
client := NewWeComClient("your_corp_id", "your_corp_secret")
resp, err := client.SendTextMessage(
    "UserID1|UserID2", // toUser
    "",                // toParty
    "",                // toTag
    1000001,           // agentID
    "你的快递已到，请携带工卡前往邮件中心领取。",
)

// 带选项的用法
resp, err := client.SendTextMessage(
    "@all",    // 发送给所有人
    "",        // toParty
    "",        // toTag
    1000001,   // agentID
    "重要通知：请所有员工注意查收。",
    WithSafe(1),                    // 保密消息
    WithIDTrans(1),                 // 开启ID转译
    WithDuplicateCheck(1, 1800),    // 开启重复消息检查，间隔1800秒
)
```

### 注意事项
- toUser、toParty、toTag不能同时为空
- 如果部分接收人无权限或不存在，发送仍然执行，但会返回无效的部分
- 支持HTML标签，如`<a href="url">链接文本</a>`
- 频率限制：每应用不可超过账号上限数*200人次/天
- 每应用对同一个成员不可超过30次/分钟，1000次/小时

---

## 图文消息发送

### 简要描述
发送图文消息到企业微信，支持1到8条图文，可包含链接或小程序跳转。

### 请求方法
```go
func (client *Client) SendNewsMessage(toUser, toParty, toTag string, agentID int, articles []Article, options ...MessageOption) (*MessageResp, error)
```

### 参数说明

| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| toUser | 否 | string | 指定接收消息的成员，成员ID列表（多个接收者用'\|'分隔，最多支持1000个） |
| toParty | 否 | string | 指定接收消息的部门，部门ID列表，多个接收者用'\|'分隔，最多支持100个 |
| toTag | 否 | string | 指定接收消息的标签，标签ID列表，多个接收者用'\|'分隔，最多支持100个 |
| agentID | 是 | int | 企业应用的id |
| articles | 是 | []Article | 图文消息，一个图文消息支持1到8条图文 |
| options | 否 | ...MessageOption | 可选参数，支持WithIDTrans、WithDuplicateCheck |

### Article结构说明

| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| Title | 是 | string | 标题，不超过128个字节，超过会自动截断（支持id转译） |
| Description | 否 | string | 描述，不超过512个字节，超过会自动截断（支持id转译） |
| URL | 否 | string | 点击后跳转的链接。最长2048字节，请确保包含了协议头(http/https)，小程序或者url必须填写一个 |
| PicURL | 否 | string | 图文消息的图片链接，最长2048字节，支持JPG、PNG格式，较好的效果为大图1068*455，小图150*150 |
| AppID | 否 | string | 小程序appid，必须是与当前应用关联的小程序，appid和pagepath必须同时填写，填写后会忽略url字段 |
| PagePath | 否 | string | 点击消息卡片后的小程序页面，最长128字节，仅限本小程序内的页面。appid和pagepath必须同时填写，填写后会忽略url字段 |

### 使用示例
```go
// 基本图文消息
articles := []Article{
    {
        Title:       "中秋节礼品领取",
        Description: "今年中秋节公司有豪礼相送",
        URL:         "https://example.com/gift",
        PicURL:      "https://example.com/gift.jpg",
    },
}

client := NewWeComClient("your_corp_id", "your_corp_secret")
resp, err := client.SendNewsMessage(
    "UserID1|UserID2", // toUser
    "",                // toParty
    "",                // toTag
    1000001,           // agentID
    articles,
)

// 小程序图文消息
articlesWithMiniProgram := []Article{
    {
        Title:       "查看工资条",
        Description: "点击查看本月工资详情",
        AppID:       "wx123123123123123",
        PagePath:    "pages/salary?userid=zhangsan",
        PicURL:      "https://example.com/salary.jpg",
    },
}

resp, err := client.SendNewsMessage(
    "zhangsan",  // 发送给特定用户
    "",          // toParty
    "",          // toTag
    1000001,     // agentID
    articlesWithMiniProgram,
    WithIDTrans(1),                 // 开启ID转译
    WithDuplicateCheck(1, 3600),    // 开启重复消息检查
)
```

---

## 消息选项

### WithSafe(safe int)
设置保密消息选项，仅对文本消息有效。
- `safe`: 0表示可对外分享，1表示不能分享且内容显示水印，默认为0

### WithIDTrans(enable int)
设置ID转译选项。
- `enable`: 0表示否，1表示是，默认0

### WithDuplicateCheck(enable int, interval int)
设置重复消息检查选项。
- `enable`: 0表示否，1表示是，默认0
- `interval`: 重复消息检查的时间间隔，默认1800s，最大不超过4小时

---

## 错误处理

当API调用失败时，会返回`WeChatAPIError`类型的错误，包含错误码和错误信息：

```go
resp, err := client.SendTextMessage(...)
if err != nil {
    if weErr, ok := err.(*WeChatAPIError); ok {
        fmt.Printf("微信API错误: %d - %s\n", weErr.ErrCode, weErr.ErrMsg)
    } else {
        fmt.Printf("其他错误: %v\n", err)
    }
}
```
