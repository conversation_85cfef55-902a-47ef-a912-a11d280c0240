package wecom

import (
	"encoding/json"
	"fmt"
	"marketing/internal/pkg/log"

	"go.uber.org/zap"
)

// TextMessageReq 文本消息请求结构
type TextMessageReq struct {
	ToUser                 string      `json:"touser,omitempty"`
	ToParty                string      `json:"toparty,omitempty"`
	ToTag                  string      `json:"totag,omitempty"`
	MsgType                string      `json:"msgtype"`
	AgentID                int         `json:"agentid"`
	Text                   TextContent `json:"text"`
	Safe                   int         `json:"safe,omitempty"`
	EnableIDTrans          int         `json:"enable_id_trans,omitempty"`
	EnableDuplicateCheck   int         `json:"enable_duplicate_check,omitempty"`
	DuplicateCheckInterval int         `json:"duplicate_check_interval,omitempty"`
}

// TextContent 文本消息内容
type TextContent struct {
	Content string `json:"content"`
}

// NewsMessageReq 图文消息请求结构
type NewsMessageReq struct {
	ToUser                 string      `json:"touser,omitempty"`
	ToParty                string      `json:"toparty,omitempty"`
	ToTag                  string      `json:"totag,omitempty"`
	MsgType                string      `json:"msgtype"`
	AgentID                int         `json:"agentid"`
	News                   NewsContent `json:"news"`
	EnableIDTrans          int         `json:"enable_id_trans,omitempty"`
	EnableDuplicateCheck   int         `json:"enable_duplicate_check,omitempty"`
	DuplicateCheckInterval int         `json:"duplicate_check_interval,omitempty"`
}

// NewsContent 图文消息内容
type NewsContent struct {
	Articles []Article `json:"articles"`
}

// Article 图文消息文章
type Article struct {
	Title       string `json:"title"`
	Description string `json:"description,omitempty"`
	URL         string `json:"url,omitempty"`
	PicURL      string `json:"picurl,omitempty"`
	AppID       string `json:"appid,omitempty"`
	PagePath    string `json:"pagepath,omitempty"`
}

// MessageResp 消息发送响应
type MessageResp struct {
	ErrCode        int    `json:"errcode"`
	ErrMsg         string `json:"errmsg"`
	InvalidUser    string `json:"invaliduser,omitempty"`
	InvalidParty   string `json:"invalidparty,omitempty"`
	InvalidTag     string `json:"invalidtag,omitempty"`
	UnlicensedUser string `json:"unlicenseduser,omitempty"`
	MsgID          string `json:"msgid,omitempty"`
	ResponseCode   string `json:"response_code,omitempty"`
}

// SendTextMessage 发送文本消息
func (client *Client) SendTextMessage(toUser, toParty, toTag string, agentID int, content string, options ...MessageOption) (*MessageResp, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s", accessToken)

	reqBody := TextMessageReq{
		ToUser:  toUser,
		ToParty: toParty,
		ToTag:   toTag,
		MsgType: "text",
		AgentID: agentID,
		Text: TextContent{
			Content: content,
		},
	}

	// 应用可选参数
	for _, option := range options {
		option.applyToTextMessage(&reqBody)
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		log.Error("企微发送文本消息请求错误：", zap.Error(err))
		return nil, err
	}

	log.Debug("企微发送文本消息：", zap.Any("企微发送文本消息响应", resp))

	var messageResp MessageResp
	if err := json.Unmarshal(resp.Body(), &messageResp); err != nil {
		log.Error("企微发送文本消息响应解析错误：", zap.Error(err))
		return nil, err
	}

	if messageResp.ErrCode != 0 {
		log.Error("企微发送文本消息接口错误：", zap.Int("errcode", messageResp.ErrCode), zap.String("errmsg", messageResp.ErrMsg), zap.String("URL", url))
		return &messageResp, &WeChatAPIError{ErrCode: messageResp.ErrCode, ErrMsg: messageResp.ErrMsg}
	}

	return &messageResp, nil
}

// SendNewsMessage 发送图文消息
func (client *Client) SendNewsMessage(toUser, toParty, toTag string, agentID int, articles []Article, options ...MessageOption) (*MessageResp, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s", accessToken)

	reqBody := NewsMessageReq{
		ToUser:  toUser,
		ToParty: toParty,
		ToTag:   toTag,
		MsgType: "news",
		AgentID: agentID,
		News: NewsContent{
			Articles: articles,
		},
	}

	// 应用可选参数
	for _, option := range options {
		option.applyToNewsMessage(&reqBody)
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		log.Error("企微发送图文消息请求错误：", zap.Error(err))
		return nil, err
	}

	log.Debug("企微发送图文消息：", zap.Any("企微发送图文消息响应", resp))

	var messageResp MessageResp
	if err := json.Unmarshal(resp.Body(), &messageResp); err != nil {
		log.Error("企微发送图文消息响应解析错误：", zap.Error(err))
		return nil, err
	}

	if messageResp.ErrCode != 0 {
		log.Error("企微发送图文消息接口错误：", zap.Int("errcode", messageResp.ErrCode), zap.String("errmsg", messageResp.ErrMsg), zap.String("URL", url))
		return &messageResp, &WeChatAPIError{ErrCode: messageResp.ErrCode, ErrMsg: messageResp.ErrMsg}
	}

	return &messageResp, nil
}

// MessageOption 消息选项接口
type MessageOption interface {
	applyToTextMessage(*TextMessageReq)
	applyToNewsMessage(*NewsMessageReq)
}

// SafeOption 保密消息选项
type SafeOption struct {
	Safe int
}

func (o SafeOption) applyToTextMessage(req *TextMessageReq) {
	req.Safe = o.Safe
}

func (o SafeOption) applyToNewsMessage(req *NewsMessageReq) {
	// 图文消息不支持safe参数
}

// WithSafe 设置保密消息
func WithSafe(safe int) MessageOption {
	return SafeOption{Safe: safe}
}

// IDTransOption ID转译选项
type IDTransOption struct {
	EnableIDTrans int
}

func (o IDTransOption) applyToTextMessage(req *TextMessageReq) {
	req.EnableIDTrans = o.EnableIDTrans
}

func (o IDTransOption) applyToNewsMessage(req *NewsMessageReq) {
	req.EnableIDTrans = o.EnableIDTrans
}

// WithIDTrans 设置ID转译
func WithIDTrans(enable int) MessageOption {
	return IDTransOption{EnableIDTrans: enable}
}

// DuplicateCheckOption 重复消息检查选项
type DuplicateCheckOption struct {
	EnableDuplicateCheck   int
	DuplicateCheckInterval int
}

func (o DuplicateCheckOption) applyToTextMessage(req *TextMessageReq) {
	req.EnableDuplicateCheck = o.EnableDuplicateCheck
	req.DuplicateCheckInterval = o.DuplicateCheckInterval
}

func (o DuplicateCheckOption) applyToNewsMessage(req *NewsMessageReq) {
	req.EnableDuplicateCheck = o.EnableDuplicateCheck
	req.DuplicateCheckInterval = o.DuplicateCheckInterval
}

// WithDuplicateCheck 设置重复消息检查
func WithDuplicateCheck(enable int, interval int) MessageOption {
	return DuplicateCheckOption{
		EnableDuplicateCheck:   enable,
		DuplicateCheckInterval: interval,
	}
}
